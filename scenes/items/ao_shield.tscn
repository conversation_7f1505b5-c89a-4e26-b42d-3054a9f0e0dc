[gd_scene load_steps=4 format=3 uid="uid://hb2sga12bblt"]

[ext_resource type="Script" uid="uid://bl5t8sm860t7d" path="res://scenes/items/shield.gd" id="1_bc5sw"]
[ext_resource type="Texture2D" uid="uid://bd2lhn0jx86ud" path="res://images/items/AMPOA0.png" id="1_y33yk"]

[sub_resource type="BoxShape3D" id="BoxShape3D_y33yk"]
size = Vector3(0.690826, 0.627703, 0.672795)

[node name="AoShield" type="Area3D" groups=["item"]]
collision_layer = 8
script = ExtResource("1_bc5sw")

[node name="Sprite3D" type="Sprite3D" parent="."]
transform = Transform3D(0.33, 0, 0, 0, 0.33, 0, 0, 0, 0.33, 0, 0, 0)
billboard = 2
shaded = true
texture_filter = 1
texture = ExtResource("1_y33yk")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.00657654, -0.0150405, -0.015641)
shape = SubResource("BoxShape3D_y33yk")

[connection signal="body_entered" from="." to="." method="_on_body_entered"]
